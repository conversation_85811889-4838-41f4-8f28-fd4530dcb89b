Config = {}

-- Debug mode
Config.Debug = false

-- Refresh time pro respawn <PERSON> (v minutách)
Config.RefreshTime = 5

-- Item k<PERSON> se dá do inventáře po ukradení značky
Config.RewardItem = 'ammo-9'
Config.RewardAmount = 1

-- <PERSON><PERSON> (kde se ne<PERSON>)
Config.BlacklistZones = {
    {
        name = "Police Station",
        coords = vector3(425.1, -979.5, 30.7),
        radius = 100.0,
        debug = false
    },
    {
        name = "Hospital",
        coords = vector3(298.6, -584.4, 43.3),
        radius = 75.0,
        debug = false
    },
    {
        name = "Airport",
        coords = vector3(-1037.8, -2737.6, 20.2),
        radius = 200.0,
        debug = false
    }
}

-- <PERSON><PERSON> dopravn<PERSON><PERSON> zna<PERSON>ek
Config.SignModels = {
    `prop_sign_road_01a`,
    `prop_sign_road_02a`,
    `prop_sign_road_03a`,
    `prop_sign_road_04a`,
    `prop_sign_road_05a`,
    `prop_sign_road_06a`,
    `prop_sign_road_07a`,
    `prop_sign_road_08a`,
    `prop_sign_road_09a`,
    `prop_sign_road_restriction_10`,
    `prop_sign_road_restriction_07`,
    `prop_sign_road_restriction_08`,
    `prop_sign_road_restriction_09`,
    `prop_sign_road_restriction_02`,
    `prop_sign_road_restriction_03`,
    `prop_sign_road_restriction_04`,
    `prop_sign_road_restriction_05`,
    `prop_sign_road_restriction_06`,
    `prop_sign_road_restriction_01`,
    `prop_sign_freewayentrance`,
    `prop_sign_freeway_01a`,
    `prop_sign_freeway_02a`,
    `prop_sign_freeway_03a`,
    `prop_sign_freeway_04a`,
    `prop_sign_freeway_05a`,
    `prop_sign_freeway_06a`,
    `prop_sign_freeway_07a`,
    `prop_sign_freeway_08a`,
    `prop_sign_freeway_09a`,
    `prop_sign_freeway_10a`,
    `prop_sign_freeway_11a`,
    `prop_sign_freeway_12a`,
    `prop_sign_freeway_13a`,
    `prop_sign_freeway_14a`,
    `prop_sign_freeway_15a`,
    `prop_sign_freeway_16a`,
    `prop_sign_freeway_17a`,
    `prop_sign_freeway_18a`,
    `prop_sign_freeway_19a`,
    `prop_sign_freeway_20a`
}

-- Vzdálenost pro detekci značek
Config.DetectionDistance = 50.0

-- Vzdálenost pro interakci
Config.InteractionDistance = 3.0

-- Čas pro ukradení značky (v sekundách)
Config.StealTime = 5.0

-- Texty
Config.Texts = {
    steal_sign = "Ukrást dopravní značku",
    stealing = "Kradete dopravní značku...",
    stolen_success = "Úspěšně jste ukradli dopravní značku!",
    blacklist_zone = "V této oblasti nemůžete krást dopravní značky!",
    already_stolen = "Tato značka už byla ukradena!",
    too_far = "Jste příliš daleko od značky!"
}
