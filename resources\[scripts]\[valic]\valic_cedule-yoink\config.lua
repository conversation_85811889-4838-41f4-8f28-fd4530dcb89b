Config = {}

-- Debug mode
Config.Debug = false

-- Refresh time pro respawn <PERSON><PERSON><PERSON><PERSON> (v minut<PERSON>ch)
Config.RefreshTime = 5

-- Re<PERSON>s podle typu <PERSON> (zatím všechno ammo-9 pro dev)
Config.SignRewards = {
    -- <PERSON>áklad<PERSON><PERSON> dopra<PERSON>
    [`prop_sign_road_01a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_02a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_03a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_04a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_05a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_06a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_07a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_08a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_09a`] = { item = 'ammo-9', amount = 1 },

    -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    [`prop_sign_road_restriction_10`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_restriction_07`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_restriction_08`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_restriction_09`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_restriction_02`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_restriction_03`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_restriction_04`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_restriction_05`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_restriction_06`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_road_restriction_01`] = { item = 'ammo-9', amount = 1 },

    -- Dálniční značky
    [`prop_sign_freewayentrance`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_01a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_02a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_03a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_04a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_05a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_06a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_07a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_08a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_09a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_10a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_11a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_12a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_13a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_14a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_15a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_16a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_17a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_18a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_19a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_20a`] = { item = 'ammo-9', amount = 1 }
}

-- Fallback reward pro neznámé značky
Config.DefaultReward = { item = 'ammo-9', amount = 1 }

-- Blacklist zóny (kde se nedá krást) - Polygony
Config.BlacklistZones = {
    {
        name = "Police Station",
        points = {
            vector3(370.0, -1020.0, 29.0),
            vector3(480.0, -1020.0, 29.0),
            vector3(480.0, -940.0, 29.0),
            vector3(370.0, -940.0, 29.0)
        },
        minZ = 25.0,
        maxZ = 50.0,
        debug = false,
        debugTarget = vector3(425.1, -979.5, 30.7) -- Pozice debug targetu
    },
    {
        name = "Hospital",
        points = {
            vector3(250.0, -620.0, 40.0),
            vector3(350.0, -620.0, 40.0),
            vector3(350.0, -550.0, 40.0),
            vector3(250.0, -550.0, 40.0)
        },
        minZ = 35.0,
        maxZ = 55.0,
        debug = false,
        debugTarget = vector3(298.6, -584.4, 43.3)
    },
    {
        name = "Airport",
        points = {
            vector3(-1200.0, -2800.0, 15.0),
            vector3(-900.0, -2800.0, 15.0),
            vector3(-900.0, -2650.0, 15.0),
            vector3(-1200.0, -2650.0, 15.0)
        },
        minZ = 10.0,
        maxZ = 30.0,
        debug = false,
        debugTarget = vector3(-1037.8, -2737.6, 20.2)
    }
}

-- Modely dopravních značek
Config.SignModels = {
    `prop_sign_road_01a`,
    `prop_sign_road_02a`,
    `prop_sign_road_03a`,
    `prop_sign_road_04a`,
    `prop_sign_road_05a`,
    `prop_sign_road_06a`,
    `prop_sign_road_07a`,
    `prop_sign_road_08a`,
    `prop_sign_road_09a`,
    `prop_sign_road_restriction_10`,
    `prop_sign_road_restriction_07`,
    `prop_sign_road_restriction_08`,
    `prop_sign_road_restriction_09`,
    `prop_sign_road_restriction_02`,
    `prop_sign_road_restriction_03`,
    `prop_sign_road_restriction_04`,
    `prop_sign_road_restriction_05`,
    `prop_sign_road_restriction_06`,
    `prop_sign_road_restriction_01`,
    `prop_sign_freewayentrance`,
    `prop_sign_freeway_01a`,
    `prop_sign_freeway_02a`,
    `prop_sign_freeway_03a`,
    `prop_sign_freeway_04a`,
    `prop_sign_freeway_05a`,
    `prop_sign_freeway_06a`,
    `prop_sign_freeway_07a`,
    `prop_sign_freeway_08a`,
    `prop_sign_freeway_09a`,
    `prop_sign_freeway_10a`,
    `prop_sign_freeway_11a`,
    `prop_sign_freeway_12a`,
    `prop_sign_freeway_13a`,
    `prop_sign_freeway_14a`,
    `prop_sign_freeway_15a`,
    `prop_sign_freeway_16a`,
    `prop_sign_freeway_17a`,
    `prop_sign_freeway_18a`,
    `prop_sign_freeway_19a`,
    `prop_sign_freeway_20a`
}

-- Vzdálenost pro detekci značek
Config.DetectionDistance = 50.0

-- Vzdálenost pro interakci
Config.InteractionDistance = 3.0

-- Čas pro ukradení značky (v sekundách)
Config.StealTime = 5.0

-- Texty
Config.Texts = {
    steal_sign = "Ukrást dopravní značku",
    stealing = "Kradete dopravní značku...",
    stolen_success = "Úspěšně jste ukradli dopravní značku!",
    blacklist_zone = "V této oblasti nemůžete krást dopravní značky!",
    already_stolen = "Tato značka už byla ukradena!",
    too_far = "Jste příliš daleko od značky!"
}
