# Val<PERSON>dule <PERSON>ink - <PERSON><PERSON><PERSON><PERSON><PERSON> dopravních značek

## <PERSON>is
Script umožňuje hráčům krást dopravní značky po celé mapě pomocí ox_target systému. Ukradené značky se automaticky obnoví po nastaveném čase.

## Funkce
- ✅ Kr<PERSON><PERSON><PERSON> dopravních značek pomocí ox_target
- ✅ Blacklist zóny kde se nedá krást
- ✅ Debug mode pro testování
- ✅ Automatické obnovení značek po 5 minutách
- ✅ Reward system (přidání itemu do inventáře)
- ✅ Progress bar při krádeži
- ✅ Admin příkazy

## Instalace
1. Zkopírujte složku do `resources/[scripts]/[valic]/`
2. Přidejte do `server.cfg`: `ensure valic_cedule-yoink`
3. <PERSON>jist<PERSON><PERSON> se, že máte nainstalované:
   - ox_inventory
   - ox_target
   - ox_lib (pro notifikace a progress bar)

## Konfigurace
Veškeré nastavení najdete v `config.lua`:

### Základní nastavení
- `Config.Debug` - Debug mode (false/true)
- `Config.RefreshTime` - Čas obnovení značek v minutách (default: 5)
- `Config.RewardItem` - Item který se přidá do inventáře (default: 'ammo-9')
- `Config.RewardAmount` - Množství itemu (default: 1)

### Blacklist zóny
```lua
Config.BlacklistZones = {
    {
        name = "Police Station",
        coords = vector3(425.1, -979.5, 30.7),
        radius = 100.0,
        debug = false
    }
}
```

### Modely značek
Script automaticky detekuje všechny běžné modely dopravních značek v GTA V.

## Příkazy

### Admin příkazy (vyžadují ACE permission "cedule.admin")
- `/cedule_debug` - Zapne/vypne debug mode
- `/cedule_zone_debug <číslo>` - Zapne/vypne debug pro konkrétní blacklist zónu
- `/cedule_reload` - Restartuje resource
- `/cedule_info` - Zobrazí informace o scriptu

### Console příkazy
Všechny admin příkazy fungují i z console bez potřeby permissions.

## Permissions
Přidejte do `server.cfg` nebo ACE systému:
```
add_ace group.admin cedule.admin allow
```

## Debug mode
Když je zapnutý debug mode:
- Zobrazují se debug zprávy v konzoli
- Můžete zapnout vizuální zobrazení blacklist zón

## Jak to funguje
1. Hráč se přiblíží k dopravní značce
2. Objeví se ox_target možnost "Ukrást dopravní značku"
3. Po kliknutí se spustí progress bar (5 sekund)
4. Po dokončení se značka skryje a hráč dostane reward item
5. Značka se automaticky obnoví po 5 minutách

## Blacklist zóny
V blacklist zónách se nedá krást. Defaultně jsou nastavené:
- Police Station (100m radius)
- Hospital (75m radius)
- Airport (200m radius)

## Troubleshooting

### Script se nespustí
- Zkontrolujte že máte ox_inventory a ox_target
- Zkontrolujte console pro chyby

### Targety se nezobrazují
- Ujistěte se že je ox_target správně nakonfigurován
- Zkontrolujte že jste dostatečně blízko značce

### Items se nepřidávají do inventáře
- Zkontrolujte že item 'ammo-9' existuje v ox_inventory
- Změňte `Config.RewardItem` na existující item

## Customizace
Můžete snadno upravit:
- Modely značek v `Config.SignModels`
- Blacklist zóny v `Config.BlacklistZones`
- Reward items v `Config.RewardItem`
- Časy a vzdálenosti

## Podpora
Pro podporu kontaktujte Valic nebo vytvořte issue.

## Verze
- v1.0.0 - Základní funkcionalita
