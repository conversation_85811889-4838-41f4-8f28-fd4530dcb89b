# Valic <PERSON>dule <PERSON>ink - <PERSON><PERSON><PERSON><PERSON><PERSON> dopravních znač<PERSON>

## <PERSON>is
Script umožňuje hráčům krást dopravní značky po celé mapě pomocí ox_target systému. Ukradené značky se automaticky obnoví po nastaveném čase.

## Funkce
- ✅ Krádež dopravních značek pomocí ox_target
- ✅ Blacklist zóny (polygony) kde se nedá krást
- ✅ Debug mode s vizuálním zobrazením polygon zón
- ✅ Debug targety přímo v blacklist zónách
- ✅ Automatické obnovení značek po 5 minutách
- ✅ Různé rewards podle typu značky
- ✅ Progress bar při krádeži
- ✅ Admin příkazy

## Instalace
1. Zkopírujte složku do `resources/[scripts]/[valic]/`
2. Přidejte do `server.cfg`: `ensure valic_cedule-yoink`
3. <PERSON><PERSON><PERSON><PERSON><PERSON> se, že máte nainstalované:
   - **QBX Core** (framework)
   - **ox_lib** (pro notifikace, progress bar a příkazy)
   - **ox_inventory** (pro items)
   - **ox_target** (pro targeting)
4. Přidejte admin permissions do `server.cfg`:
   ```
   add_ace group.admin command allow
   ```

## Konfigurace
Veškeré nastavení najdete v `config.lua`:

### Základní nastavení
- `Config.Debug` - Debug mode (false/true)
- `Config.RefreshTime` - Čas obnovení značek v minutách (default: 5)
- `Config.RewardItem` - Item který se přidá do inventáře (default: 'ammo-9')
- `Config.RewardAmount` - Množství itemu (default: 1)

### Blacklist zóny (Polygony)
```lua
Config.BlacklistZones = {
    {
        name = "Police Station",
        points = {
            vector3(370.0, -1020.0, 29.0),
            vector3(480.0, -1020.0, 29.0),
            vector3(480.0, -940.0, 29.0),
            vector3(370.0, -940.0, 29.0)
        },
        minZ = 25.0,
        maxZ = 50.0,
        debug = false,
        debugTarget = vector3(425.1, -979.5, 30.7) -- Pozice debug targetu
    }
}
```

### Rewards podle typu značky
```lua
Config.SignRewards = {
    [`prop_sign_road_01a`] = { item = 'ammo-9', amount = 1 },
    [`prop_sign_freeway_01a`] = { item = 'ammo-9', amount = 1 },
    -- ... další značky
}
```

### Modely značek
Script automaticky detekuje všechny běžné modely dopravních značek v GTA V.

## Příkazy

### Admin příkazy (vyžadují group.admin permission)
- `/cedule_debug` - Zapne/vypne debug mode
- `/cedule_zones` - Zobrazí seznam blacklist zón
- `/cedule_reload` - Restartuje resource
- `/cedule_info` - Zobrazí informace o scriptu (dostupné pro všechny)

### Console příkazy
Všechny admin příkazy fungují i z console bez potřeby permissions.

## Permissions (QBX Core)
Script používá QBX Core permission systém přes ox_lib:
```
add_ace group.admin command allow
```
Admin příkazy jsou omezené na `group.admin`.

## Debug mode
Když je zapnutý debug mode:
- Zobrazují se debug zprávy v konzoli
- Můžete zapnout vizuální zobrazení blacklist polygon zón
- Debug targety se nacházejí přímo v blacklist zónách
- Kliknutím na debug target zapnete/vypnete vizualizaci dané zóny

## Jak to funguje
1. Hráč se přiblíží k dopravní značce
2. Objeví se ox_target možnost "Ukrást dopravní značku"
3. Po kliknutí se spustí progress bar (5 sekund)
4. Po dokončení se značka skryje a hráč dostane reward item
5. Značka se automaticky obnoví po 5 minutách

## Blacklist zóny
V blacklist polygon zónách se nedá krást. Defaultně jsou nastavené:
- Police Station (polygon kolem PD)
- Hospital (polygon kolem nemocnice)
- Airport (polygon kolem letiště)

Každá zóna má:
- **Debug target** - ox_target sféra pro zapnutí/vypnutí debug módu
- **Polygon** - definovaný pomocí vec3 bodů
- **MinZ/MaxZ** - výškové omezení zóny

## Troubleshooting

### Script se nespustí
- Zkontrolujte že máte ox_inventory a ox_target
- Zkontrolujte console pro chyby

### Targety se nezobrazují
- Ujistěte se že je ox_target správně nakonfigurován
- Zkontrolujte že jste dostatečně blízko značce

### Items se nepřidávají do inventáře
- Zkontrolujte že item 'ammo-9' existuje v ox_inventory
- Změňte `Config.RewardItem` na existující item

## Customizace
Můžete snadno upravit:
- Modely značek v `Config.SignModels`
- Blacklist zóny v `Config.BlacklistZones`
- Reward items v `Config.RewardItem`
- Časy a vzdálenosti

## Podpora
Pro podporu kontaktujte Valic nebo vytvořte issue.

## Verze
- v1.0.0 - Základní funkcionalita
