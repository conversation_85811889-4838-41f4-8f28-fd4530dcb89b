local stolenSigns = {}
local nearbyTargets = {}
local debugTargets = {}

-- Debug funkce
local function debugPrint(message)
    if Config.Debug then
        print("^3[DEBUG]^7 " .. message)
    end
end

-- Funkce pro kontrolu jestli je bod uvnitř polygonu
local function isPointInPolygon(point, polygon)
    local x, y = point.x, point.y
    local inside = false
    local j = #polygon

    for i = 1, #polygon do
        local xi, yi = polygon[i].x, polygon[i].y
        local xj, yj = polygon[j].x, polygon[j].y

        if ((yi > y) ~= (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi) then
            inside = not inside
        end
        j = i
    end

    return inside
end

-- Funkce pro kreslení debug polygonů
local function drawDebugZones()
    for _, zone in pairs(Config.BlacklistZones) do
        if zone.debug then
            -- Kreslení polygonu
            for i = 1, #zone.points do
                local current = zone.points[i]
                local next = zone.points[i + 1] or zone.points[1]

                -- Spodní čára
                DrawLine(current.x, current.y, zone.minZ, next.x, next.y, zone.minZ, 255, 0, 0, 255)
                -- Horní čára
                DrawLine(current.x, current.y, zone.maxZ, next.x, next.y, zone.maxZ, 255, 0, 0, 255)
                -- Vertikální čáry
                DrawLine(current.x, current.y, zone.minZ, current.x, current.y, zone.maxZ, 255, 0, 0, 255)
            end

            -- Text s názvem zóny
            local center = vector3(0, 0, 0)
            for _, point in pairs(zone.points) do
                center = center + point
            end
            center = center / #zone.points
            center = vector3(center.x, center.y, zone.maxZ + 2.0)

            DrawText3D(center.x, center.y, center.z, zone.name .. " (BLACKLIST)", 255, 0, 0)
        end
    end
end

-- Funkce pro kreslení 3D textu
function DrawText3D(x, y, z, text, r, g, b)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    if onScreen then
        SetTextScale(0.35, 0.35)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(r, g, b, 215)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

-- Kontrola jestli je hráč v blacklist zóně
local function isInBlacklistZone(coords)
    for _, zone in pairs(Config.BlacklistZones) do
        if coords.z >= zone.minZ and coords.z <= zone.maxZ then
            if isPointInPolygon(coords, zone.points) then
                debugPrint("Hráč je v blacklist zóně: " .. zone.name)
                return true, zone.name
            end
        end
    end
    return false
end

-- Funkce pro ukradení značky
local function stealSign(entity)
    local playerCoords = GetEntityCoords(PlayerPedId())
    local entityCoords = GetEntityCoords(entity)
    local distance = #(playerCoords - entityCoords)

    if distance > Config.InteractionDistance then
        exports.ox_lib:notify({
            title = 'Chyba',
            description = Config.Texts.too_far,
            type = 'error'
        })
        return
    end

    local inBlacklist, zoneName = isInBlacklistZone(playerCoords)
    if inBlacklist then
        exports.ox_lib:notify({
            title = 'Zakázaná oblast',
            description = Config.Texts.blacklist_zone .. " (" .. zoneName .. ")",
            type = 'error'
        })
        return
    end

    local entityId = NetworkGetNetworkIdFromEntity(entity)
    if stolenSigns[entityId] then
        exports.ox_lib:notify({
            title = 'Chyba',
            description = Config.Texts.already_stolen,
            type = 'error'
        })
        return
    end

    -- Progress bar
    if exports.ox_lib:progressBar({
        duration = Config.StealTime * 1000,
        label = Config.Texts.stealing,
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        },
        anim = {
            dict = 'mini@repair',
            clip = 'fixing_a_ped'
        }
    }) then
        -- Úspěšně dokončeno
        stolenSigns[entityId] = true
        SetEntityVisible(entity, false, false)
        SetEntityCollision(entity, false, false)

        -- Získat model značky pro reward
        local model = GetEntityModel(entity)

        -- Odeslat na server pro přidání itemu
        TriggerServerEvent('valic_cedule:stealSign', entityId, model)

        exports.ox_lib:notify({
            title = 'Úspěch',
            description = Config.Texts.stolen_success,
            type = 'success'
        })

        debugPrint("Značka ukradena: " .. entityId .. " (model: " .. model .. ")")

        -- Odstranit target
        exports.ox_target:removeEntity(entity)
    end
end

-- Funkce pro přidání targetu na značku
local function addSignTarget(entity)
    local entityId = NetworkGetNetworkIdFromEntity(entity)
    
    if stolenSigns[entityId] then
        return
    end
    
    exports.ox_target:addEntity(entity, {
        {
            name = 'steal_sign_' .. entityId,
            icon = 'fas fa-hand-paper',
            label = Config.Texts.steal_sign,
            distance = Config.InteractionDistance,
            onSelect = function()
                stealSign(entity)
            end
        }
    })
    
    nearbyTargets[entityId] = entity
    debugPrint("Target přidán na značku: " .. entityId)
end

-- Funkce pro odstranění targetu
local function removeSignTarget(entity)
    local entityId = NetworkGetNetworkIdFromEntity(entity)
    
    if nearbyTargets[entityId] then
        exports.ox_target:removeEntity(entity)
        nearbyTargets[entityId] = nil
        debugPrint("Target odstraněn ze značky: " .. entityId)
    end
end

-- Funkce pro přidání debug targetu
local function addDebugTarget(zone, index)
    local coords = zone.debugTarget

    exports.ox_target:addSphereZone({
        coords = coords,
        radius = 2.0,
        options = {
            {
                name = 'debug_zone_' .. index,
                icon = 'fas fa-bug',
                label = 'Debug: ' .. zone.name,
                distance = 2.0,
                onSelect = function()
                    zone.debug = not zone.debug
                    exports.ox_lib:notify({
                        title = 'Debug Zone',
                        description = 'Debug pro zónu "' .. zone.name .. '": ' .. (zone.debug and 'ZAPNUT' or 'VYPNUT'),
                        type = 'info'
                    })
                    debugPrint("Debug pro zónu " .. zone.name .. ": " .. (zone.debug and "ZAPNUT" or "VYPNUT"))
                end
            }
        }
    })

    debugTargets[index] = true
    debugPrint("Debug target přidán pro zónu: " .. zone.name)
end

-- Inicializace debug targetů
CreateThread(function()
    Wait(2000) -- Počkat až se vše načte

    for i, zone in ipairs(Config.BlacklistZones) do
        addDebugTarget(zone, i)
    end
end)

-- Hlavní loop pro detekci značek
CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local currentTargets = {}

        -- Najít všechny značky v okolí
        for _, model in pairs(Config.SignModels) do
            local objects = GetGamePool('CObject')
            for _, obj in pairs(objects) do
                if GetEntityModel(obj) == model then
                    local objCoords = GetEntityCoords(obj)
                    local distance = #(playerCoords - objCoords)

                    if distance <= Config.DetectionDistance then
                        local entityId = NetworkGetNetworkIdFromEntity(obj)
                        currentTargets[entityId] = obj

                        -- Přidat target pokud ještě není
                        if not nearbyTargets[entityId] and not stolenSigns[entityId] then
                            addSignTarget(obj)
                        end
                    end
                end
            end
        end

        -- Odstranit targety pro značky které jsou daleko
        for entityId, entity in pairs(nearbyTargets) do
            if not currentTargets[entityId] then
                removeSignTarget(entity)
            end
        end

        Wait(1000)
    end
end)

-- Debug rendering
CreateThread(function()
    while true do
        if Config.Debug then
            drawDebugZones()
        end
        Wait(0)
    end
end)

-- Refresh ukradených značek
CreateThread(function()
    while true do
        Wait(Config.RefreshTime * 60 * 1000) -- Převod minut na milisekundy
        
        debugPrint("Refreshing stolen signs...")
        
        for entityId, _ in pairs(stolenSigns) do
            local entity = NetworkGetEntityFromNetworkId(entityId)
            if DoesEntityExist(entity) then
                SetEntityVisible(entity, true, false)
                SetEntityCollision(entity, true, true)
                debugPrint("Značka obnovena: " .. entityId)
            end
        end
        
        stolenSigns = {}
        debugPrint("Všechny ukradené značky byly obnoveny")
    end
end)

-- Event handlery
RegisterNetEvent('valic_cedule:toggleDebug')
AddEventHandler('valic_cedule:toggleDebug', function()
    Config.Debug = not Config.Debug
    exports.ox_lib:notify({
        title = 'Debug Mode',
        description = 'Debug mode: ' .. (Config.Debug and 'ZAPNUT' or 'VYPNUT'),
        type = 'info'
    })
    debugPrint("Debug mode přepnut: " .. (Config.Debug and "ZAPNUT" or "VYPNUT"))
end)
