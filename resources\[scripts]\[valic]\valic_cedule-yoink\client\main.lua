local stolenSigns = {}
local nearbyTargets = {}

-- Debug funkce
local function debugPrint(message)
    if Config.Debug then
        print("^3[DEBUG]^7 " .. message)
    end
end

-- Funkce pro kreslení debug zón
local function drawDebugZones()
    for _, zone in pairs(Config.BlacklistZones) do
        if zone.debug then
            local coords = zone.coords
            DrawMarker(1, coords.x, coords.y, coords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 
                      zone.radius * 2.0, zone.radius * 2.0, 1.0, 255, 0, 0, 100, false, true, 2, false, nil, nil, false)
        end
    end
end

-- Kontrola jestli je hráč v blacklist zóně
local function isInBlacklistZone(coords)
    for _, zone in pairs(Config.BlacklistZones) do
        local distance = #(coords - zone.coords)
        if distance <= zone.radius then
            debugPrint("Hráč je v blacklist zóně: " .. zone.name)
            return true, zone.name
        end
    end
    return false
end

-- Funkce pro ukradení značky
local function stealSign(entity)
    local playerCoords = GetEntityCoords(PlayerPedId())
    local entityCoords = GetEntityCoords(entity)
    local distance = #(playerCoords - entityCoords)
    
    if distance > Config.InteractionDistance then
        exports.ox_lib:notify({
            title = 'Chyba',
            description = Config.Texts.too_far,
            type = 'error'
        })
        return
    end
    
    local inBlacklist, zoneName = isInBlacklistZone(playerCoords)
    if inBlacklist then
        exports.ox_lib:notify({
            title = 'Zakázaná oblast',
            description = Config.Texts.blacklist_zone .. " (" .. zoneName .. ")",
            type = 'error'
        })
        return
    end
    
    local entityId = NetworkGetNetworkIdFromEntity(entity)
    if stolenSigns[entityId] then
        exports.ox_lib:notify({
            title = 'Chyba',
            description = Config.Texts.already_stolen,
            type = 'error'
        })
        return
    end
    
    -- Progress bar
    if exports.ox_lib:progressBar({
        duration = Config.StealTime * 1000,
        label = Config.Texts.stealing,
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        },
        anim = {
            dict = 'mini@repair',
            clip = 'fixing_a_ped'
        }
    }) then
        -- Úspěšně dokončeno
        stolenSigns[entityId] = true
        SetEntityVisible(entity, false, false)
        SetEntityCollision(entity, false, false)
        
        -- Odeslat na server pro přidání itemu
        TriggerServerEvent('valic_cedule:stealSign', entityId)
        
        exports.ox_lib:notify({
            title = 'Úspěch',
            description = Config.Texts.stolen_success,
            type = 'success'
        })
        
        debugPrint("Značka ukradena: " .. entityId)
        
        -- Odstranit target
        exports.ox_target:removeEntity(entity)
    end
end

-- Funkce pro přidání targetu na značku
local function addSignTarget(entity)
    local entityId = NetworkGetNetworkIdFromEntity(entity)
    
    if stolenSigns[entityId] then
        return
    end
    
    exports.ox_target:addEntity(entity, {
        {
            name = 'steal_sign_' .. entityId,
            icon = 'fas fa-hand-paper',
            label = Config.Texts.steal_sign,
            distance = Config.InteractionDistance,
            onSelect = function()
                stealSign(entity)
            end
        }
    })
    
    nearbyTargets[entityId] = entity
    debugPrint("Target přidán na značku: " .. entityId)
end

-- Funkce pro odstranění targetu
local function removeSignTarget(entity)
    local entityId = NetworkGetNetworkIdFromEntity(entity)
    
    if nearbyTargets[entityId] then
        exports.ox_target:removeEntity(entity)
        nearbyTargets[entityId] = nil
        debugPrint("Target odstraněn ze značky: " .. entityId)
    end
end

-- Hlavní loop pro detekci značek
CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local currentTargets = {}
        
        -- Najít všechny značky v okolí
        for _, model in pairs(Config.SignModels) do
            local objects = GetGamePool('CObject')
            for _, obj in pairs(objects) do
                if GetEntityModel(obj) == model then
                    local objCoords = GetEntityCoords(obj)
                    local distance = #(playerCoords - objCoords)
                    
                    if distance <= Config.DetectionDistance then
                        local entityId = NetworkGetNetworkIdFromEntity(obj)
                        currentTargets[entityId] = obj
                        
                        -- Přidat target pokud ještě není
                        if not nearbyTargets[entityId] and not stolenSigns[entityId] then
                            addSignTarget(obj)
                        end
                    end
                end
            end
        end
        
        -- Odstranit targety pro značky které jsou daleko
        for entityId, entity in pairs(nearbyTargets) do
            if not currentTargets[entityId] then
                removeSignTarget(entity)
            end
        end
        
        Wait(1000)
    end
end)

-- Debug rendering
CreateThread(function()
    while true do
        if Config.Debug then
            drawDebugZones()
        end
        Wait(0)
    end
end)

-- Refresh ukradených značek
CreateThread(function()
    while true do
        Wait(Config.RefreshTime * 60 * 1000) -- Převod minut na milisekundy
        
        debugPrint("Refreshing stolen signs...")
        
        for entityId, _ in pairs(stolenSigns) do
            local entity = NetworkGetEntityFromNetworkId(entityId)
            if DoesEntityExist(entity) then
                SetEntityVisible(entity, true, false)
                SetEntityCollision(entity, true, true)
                debugPrint("Značka obnovena: " .. entityId)
            end
        end
        
        stolenSigns = {}
        debugPrint("Všechny ukradené značky byly obnoveny")
    end
end)

-- Event handlery
RegisterNetEvent('valic_cedule:toggleDebug')
AddEventHandler('valic_cedule:toggleDebug', function()
    Config.Debug = not Config.Debug
    exports.ox_lib:notify({
        title = 'Debug Mode',
        description = 'Debug mode: ' .. (Config.Debug and 'ZAPNUT' or 'VYPNUT'),
        type = 'info'
    })
end)

RegisterNetEvent('valic_cedule:toggleZoneDebug')
AddEventHandler('valic_cedule:toggleZoneDebug', function(zoneIndex)
    if Config.BlacklistZones[zoneIndex] then
        Config.BlacklistZones[zoneIndex].debug = not Config.BlacklistZones[zoneIndex].debug
        exports.ox_lib:notify({
            title = 'Zone Debug',
            description = 'Debug pro zónu "' .. Config.BlacklistZones[zoneIndex].name .. '": ' .. 
                         (Config.BlacklistZones[zoneIndex].debug and 'ZAPNUT' or 'VYPNUT'),
            type = 'info'
        })
    end
end)
