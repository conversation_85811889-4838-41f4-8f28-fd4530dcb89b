-- Debug funkce
local function debugPrint(message)
    if Config.Debug then
        print("^3[DEBUG SERVER]^7 " .. message)
    end
end

-- Event pro ukradení značky
RegisterNetEvent('valic_cedule:stealSign')
AddEventHandler('valic_cedule:stealSign', function(entityId)
    local source = source
    local player = exports.ox_inventory:GetPlayerItems(source)
    
    if not player then
        debugPrint("Hr<PERSON><PERSON> nenalezen: " .. source)
        return
    end
    
    -- Přidat item do inventáře
    local success = exports.ox_inventory:AddItem(source, Config.RewardItem, Config.RewardAmount)
    
    if success then
        debugPrint("Item přidán hráči " .. source .. ": " .. Config.RewardItem .. " x" .. Config.RewardAmount)
        
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Inventář',
            description = 'Získali jste: ' .. Config.RewardItem .. ' x' .. Config.RewardAmount,
            type = 'success'
        })
    else
        debugPrint("Nepodařilo se přidat item hráči " .. source)
        
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Chyba',
            description = 'Nepodařilo se přidat item do inventáře!',
            type = 'error'
        })
    end
end)

-- Admin příkazy
RegisterCommand('cedule_debug', function(source, args, rawCommand)
    if source == 0 then -- Console
        Config.Debug = not Config.Debug
        print("^2[CEDULE]^7 Debug mode: " .. (Config.Debug and "ZAPNUT" or "VYPNUT"))
        TriggerClientEvent('valic_cedule:toggleDebug', -1)
    else
        -- Zkontrolovat admin práva (můžeš přidat svůj admin system)
        local player = GetPlayerName(source)
        if IsPlayerAceAllowed(source, "cedule.admin") then
            TriggerClientEvent('valic_cedule:toggleDebug', source)
        else
            TriggerClientEvent('ox_lib:notify', source, {
                title = 'Chyba',
                description = 'Nemáte oprávnění k tomuto příkazu!',
                type = 'error'
            })
        end
    end
end, false)

RegisterCommand('cedule_zone_debug', function(source, args, rawCommand)
    local zoneIndex = tonumber(args[1])
    
    if not zoneIndex or zoneIndex < 1 or zoneIndex > #Config.BlacklistZones then
        if source == 0 then
            print("^1[CEDULE]^7 Použití: cedule_zone_debug <číslo_zóny>")
            print("^7Dostupné zóny:")
            for i, zone in ipairs(Config.BlacklistZones) do
                print("^7" .. i .. ". " .. zone.name)
            end
        else
            TriggerClientEvent('ox_lib:notify', source, {
                title = 'Chyba',
                description = 'Neplatné číslo zóny! Použijte číslo 1-' .. #Config.BlacklistZones,
                type = 'error'
            })
        end
        return
    end
    
    if source == 0 then -- Console
        Config.BlacklistZones[zoneIndex].debug = not Config.BlacklistZones[zoneIndex].debug
        print("^2[CEDULE]^7 Debug pro zónu '" .. Config.BlacklistZones[zoneIndex].name .. "': " .. 
              (Config.BlacklistZones[zoneIndex].debug and "ZAPNUT" or "VYPNUT"))
        TriggerClientEvent('valic_cedule:toggleZoneDebug', -1, zoneIndex)
    else
        -- Zkontrolovat admin práva
        if IsPlayerAceAllowed(source, "cedule.admin") then
            TriggerClientEvent('valic_cedule:toggleZoneDebug', source, zoneIndex)
        else
            TriggerClientEvent('ox_lib:notify', source, {
                title = 'Chyba',
                description = 'Nemáte oprávnění k tomuto příkazu!',
                type = 'error'
            })
        end
    end
end, false)

-- Příkaz pro reload configu
RegisterCommand('cedule_reload', function(source, args, rawCommand)
    if source == 0 or IsPlayerAceAllowed(source, "cedule.admin") then
        -- Reload config (restart resource)
        ExecuteCommand('restart valic_cedule-yoink')
        
        if source == 0 then
            print("^2[CEDULE]^7 Resource restartován!")
        else
            TriggerClientEvent('ox_lib:notify', source, {
                title = 'Úspěch',
                description = 'Resource byl restartován!',
                type = 'success'
            })
        end
    else
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Chyba',
            description = 'Nemáte oprávnění k tomuto příkazu!',
            type = 'error'
        })
    end
end, false)

-- Info příkaz
RegisterCommand('cedule_info', function(source, args, rawCommand)
    local info = {
        "^2=== VALIC CEDULE YOINK ===^7",
        "^7Verze: 1.0.0",
        "^7Autor: Valic",
        "^7",
        "^3Příkazy:^7",
        "^7/cedule_debug - Zapne/vypne debug mode",
        "^7/cedule_zone_debug <číslo> - Zapne/vypne debug pro konkrétní zónu",
        "^7/cedule_reload - Restartuje resource",
        "^7/cedule_info - Zobrazí tyto informace",
        "^7",
        "^3Konfigurace:^7",
        "^7Refresh čas: " .. Config.RefreshTime .. " minut",
        "^7Reward item: " .. Config.RewardItem .. " x" .. Config.RewardAmount,
        "^7Blacklist zón: " .. #Config.BlacklistZones,
        "^7Debug mode: " .. (Config.Debug and "ZAPNUT" or "VYPNUT")
    }
    
    if source == 0 then
        for _, line in ipairs(info) do
            print(line)
        end
    else
        for _, line in ipairs(info) do
            TriggerClientEvent('chat:addMessage', source, {
                args = { line }
            })
        end
    end
end, false)

-- Startup message
CreateThread(function()
    Wait(1000)
    print("^2=================================^7")
    print("^2    VALIC CEDULE YOINK v1.0     ^7")
    print("^2=================================^7")
    print("^7Resource úspěšně načten!")
    print("^7Použijte ^3/cedule_info^7 pro více informací")
    print("^2=================================^7")
end)
