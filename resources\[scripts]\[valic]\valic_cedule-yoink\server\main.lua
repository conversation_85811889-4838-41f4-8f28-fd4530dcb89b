-- Debug funkce
local function debugPrint(message)
    if Config.Debug then
        print("^3[DEBUG SERVER]^7 " .. message)
    end
end

-- Event pro ukradení značky
RegisterNetEvent('valic_cedule:stealSign')
AddEventHandler('valic_cedule:stealSign', function(entityId, model)
    local source = source
    local player = exports.ox_inventory:GetPlayerItems(source)

    if not player then
        debugPrint("Hráč nenalezen: " .. source)
        return
    end

    -- Získat reward podle modelu značky
    local reward = Config.SignRewards[model] or Config.DefaultReward

    -- Přidat item do inventáře
    local success = exports.ox_inventory:AddItem(source, reward.item, reward.amount)

    if success then
        debugPrint("Item přidán hráči " .. source .. ": " .. reward.item .. " x" .. reward.amount .. " (model: " .. model .. ")")

        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Inventář',
            description = 'Získali jste: ' .. reward.item .. ' x' .. reward.amount,
            type = 'success'
        })
    else
        debugPrint("Nepodařilo se přidat item hráči " .. source .. " (model: " .. model .. ")")

        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Chyba',
            description = 'Nepodařilo se přidat item do inventáře!',
            type = 'error'
        })
    end
end)

-- Admin příkazy pro QBX Core
lib.addCommand('cedule_debug', {
    help = 'Zapne/vypne debug mode pro cedule script',
    restricted = 'group.admin'
}, function(source, args, raw)
    if source == 0 then -- Console
        Config.Debug = not Config.Debug
        print("^2[CEDULE]^7 Debug mode: " .. (Config.Debug and "ZAPNUT" or "VYPNUT"))
        TriggerClientEvent('valic_cedule:toggleDebug', -1)
    else
        TriggerClientEvent('valic_cedule:toggleDebug', source)
    end
end)

lib.addCommand('cedule_zones', {
    help = 'Zobrazí seznam blacklist zón',
    restricted = 'group.admin'
}, function(source, args, raw)
    if source == 0 then
        print("^2[CEDULE]^7 Dostupné blacklist zóny:")
        for i, zone in ipairs(Config.BlacklistZones) do
            print("^7" .. i .. ". " .. zone.name .. " - Debug: " .. (zone.debug and "ZAPNUT" or "VYPNUT"))
            print("^7   Target pozice: " .. zone.debugTarget.x .. ", " .. zone.debugTarget.y .. ", " .. zone.debugTarget.z)
        end
    else
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Blacklist Zóny',
            description = 'Jděte k debug targetům v blacklist zónách pro zapnutí debug módu!',
            type = 'info'
        })

        -- Poslat seznam zón
        local zoneList = {}
        for i, zone in ipairs(Config.BlacklistZones) do
            table.insert(zoneList, {
                name = zone.name,
                debug = zone.debug,
                coords = zone.debugTarget
            })
        end

        TriggerClientEvent('chat:addMessage', source, {
            args = { "^2[CEDULE]^7 Blacklist zóny s debug targety:" }
        })

        for i, zone in ipairs(zoneList) do
            TriggerClientEvent('chat:addMessage', source, {
                args = { "^7" .. i .. ". " .. zone.name .. " - Debug: " .. (zone.debug and "^2ZAPNUT^7" or "^1VYPNUT^7") }
            })
        end
    end
end)

lib.addCommand('cedule_reload', {
    help = 'Restartuje cedule script',
    restricted = 'group.admin'
}, function(source, args, raw)
    -- Reload config (restart resource)
    ExecuteCommand('restart valic_cedule-yoink')

    if source == 0 then
        print("^2[CEDULE]^7 Resource restartován!")
    else
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Úspěch',
            description = 'Resource byl restartován!',
            type = 'success'
        })
    end
end)

lib.addCommand('cedule_info', {
    help = 'Zobrazí informace o cedule scriptu'
}, function(source, args, raw)
    local info = {
        "^2=== VALIC CEDULE YOINK ===^7",
        "^7Verze: 1.0.0",
        "^7Autor: stepan_valic",
        "^7Framework: QBX Core",
        "^7",
        "^3Příkazy:^7",
        "^7/cedule_debug - Zapne/vypne debug mode (admin)",
        "^7/cedule_zones - Zobrazí blacklist zóny (admin)",
        "^7/cedule_reload - Restartuje resource (admin)",
        "^7/cedule_info - Zobrazí tyto informace",
        "^7",
        "^3Jak používat:^7",
        "^7- Jděte k debug targetům v blacklist zónách",
        "^7- Klikněte na ně pro zapnutí/vypnutí debug módu",
        "^7- Debug zobrazí polygon zóny",
        "^7",
        "^3Konfigurace:^7",
        "^7Refresh čas: " .. Config.RefreshTime .. " minut",
        "^7Blacklist zón: " .. #Config.BlacklistZones,
        "^7Debug mode: " .. (Config.Debug and "ZAPNUT" or "VYPNUT"),
        "^7Různé rewards podle typu značky: ANO"
    }

    if source == 0 then
        for _, line in ipairs(info) do
            print(line)
        end
    else
        for _, line in ipairs(info) do
            TriggerClientEvent('chat:addMessage', source, {
                args = { line }
            })
        end
    end
end)

-- Startup message
CreateThread(function()
    Wait(1000)
    print("^2=================================^7")
    print("^2    VALIC CEDULE YOINK v1.0     ^7")
    print("^2=================================^7")
    print("^7Resource úspěšně načten!")
    print("^7Použijte ^3/cedule_info^7 pro více informací")
    print("^2=================================^7")
end)
